"""
Localization system for the Telegram bot.
Manages text strings in multiple languages using python-i18n.
"""

from typing import Dict, Optional
from enum import Enum
from .i18n_config import Language, get_text, set_locale, get_locale, get_available_locales, add_translation


class Localization:
    """
    Backward-compatible wrapper for python-i18n localization system.
    Maintains the same API as the original custom implementation.
    """

    def __init__(self, default_language: Language = Language.RUSSIAN):
        self.default_language = default_language
        # Set the locale in the i18n system
        set_locale(default_language.value)

    def get_text(self, key: str, language: Optional[Language] = None, **kwargs) -> str:
        """
        Get localized text by key.

        Args:
            key: The text key to retrieve
            language: Language to use (defaults to default_language)
            **kwargs: Format arguments for the text string

        Returns:
            Formatted localized text string
        """
        if language is None:
            language = self.default_language

        # Convert Language enum to string
        locale = language.value if isinstance(language, Language) else language

        # Convert kwargs to match python-i18n format (uses %{key} instead of {key})
        # The YAML files already use %{key} format, so we pass kwargs directly
        try:
            return get_text(key, locale=locale, **kwargs)
        except Exception:
            # Fallback error handling similar to original implementation
            return f"[Missing text: {key}]"

    def set_default_language(self, language: Language):
        """Set the default language for the localization system"""
        self.default_language = language
        set_locale(language.value)

    def get_available_languages(self) -> list[Language]:
        """Get list of available languages"""
        return list(Language)

    def add_text(self, key: str, translations: Dict[str, str]):
        """
        Add or update text translations.

        Args:
            key: The text key
            translations: Dictionary with language codes as keys and text as values
                         e.g., {"en": "Hello", "ru": "Привет"}
        """
        for locale, text in translations.items():
            add_translation(key, text, locale=locale)

    def get_all_keys(self) -> list[str]:
        """
        Get all available translation keys.
        Note: This is a simplified implementation that returns common keys.
        In a full implementation, this would scan all loaded translation files.
        """
        return [
            "rate_limit_exceeded",
            "no_response",
            "unexpected_error",
            "start_greeting",
            "help_text",
            "context_cleared",
            "context_clear_failed",
            "current_model"
        ]


# Global localization instance for backward compatibility
localization = Localization()



# Global localization instance
localization = Localization()
