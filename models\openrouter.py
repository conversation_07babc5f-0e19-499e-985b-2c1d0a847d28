import openai
import httpx
import asyncio

from typing import Optional
from models.config import config
from models.logging import logger


class OpenRouterClient:
    def __init__(self):
        self.logger = logger.get_logger("OpenRouterClient")
        self.client = openai.AsyncOpenAI(
            base_url=config.OPENROUTER_API_URL,
            api_key=config.OPENROUTER_API_KEY,
            timeout=httpx.Timeout(config.REQUEST_TIMEOUT),
        )
        self.session = httpx.AsyncClient(timeout=config.REQUEST_TIMEOUT)

    async def generate_response(self, user_id: int, messages: list) -> Optional[str]:
        headers = {
            "HTTP-Referer": config.TELEGRAM_BOT_URL,
            "X-Title": config.TELEGRAM_BOT_NAME,
        }

        for attempt in range(config.MAX_RETRIES):
            try:
                response = await self.client.chat.completions.create(
                    model=config.MODEL_NAME, messages=messages, extra_headers=headers
                )
                return response.choices[0].message.content
            except Exception as e:
                self.logger.warning(
                    f"Attempt {attempt + 1} failed for user {user_id}: {str(e)}"
                )
                if attempt == config.MAX_RETRIES - 1:
                    self.logger.error(
                        f"Failed after {config.MAX_RETRIES} attempts for user {user_id}"
                    )
                    return None
                await asyncio.sleep(1 * (attempt + 1))  # Exponential backoff
