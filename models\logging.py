import os
import logging
from logging.handlers import RotatingFileHandler
from models.config import config


class MyLogger:
    def __init__(self):
        # Create logs directory if it doesn't exist
        os.makedirs(config.LOGS_DIR, exist_ok=True)
        # Configure formatters
        self.common_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )

        self.info_handler = RotatingFileHandler(
            os.path.join(config.LOGS_DIR, "teapot_bot.log"),
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
        )
        self.info_handler.setFormatter(self.common_formatter)
        self.info_handler.setLevel(logging.INFO)

    # Configure loggers
    def get_logger(self, name) -> logging.Logger:
        logger = logging.getLogger(name=name)
        logger.addHandler(self.info_handler)
        return logger


logger = MyLogger()
