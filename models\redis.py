import redis.asyncio as redis
import json
from datetime import timedelta
from models.config import config
from models.logging import logger


class RedisContextManager:
    def __init__(self):
        self.logger = logger.get_logger("RedisContextManager")
        self.redis = redis.from_url(config.REDIS_URL)

    async def get_context(self, user_id: int) -> list:
        try:
            context = await self.redis.get(f"context:{user_id}")
            return json.loads(context) if context else []
        except Exception as e:
            self.logger.error(f"Error getting context: {e}")
            return []

    async def set_context(self, user_id: int, context: list):
        try:
            await self.redis.setex(
                f"context:{user_id}",
                timedelta(hours=24),  # Auto-expire after 24h of inactivity
                json.dumps(
                    context[-config.MAX_CONTEXT_LENGTH * 2 :]
                ),  # Trim to max length
            )
        except Exception as e:
            self.logger.error(f"Error setting context: {e}")

    async def clear_context(self, user_id: int):
        try:
            await self.redis.delete(f"context:{user_id}")
        except Exception as e:
            self.logger.error(f"Error clearing context: {e}")
