import os
from dotenv import load_dotenv

# Configuration
load_dotenv()


class Config:
    OPENROUTER_API_KEY = os.getenv("OPENROUTER_KEY", "No API key found")
    OPENROUTER_API_URL = os.getenv("OPENROUTER_URL", "https://openrouter.ai/api/v1")
    TELEGRAM_BOT_TOKEN = os.getenv("BOT_KEY", "No TG API key found")
    TELEGRAM_BOT_URL = os.getenv("BOT_URL", "https://t.me/Project_TeaPot_bot")
    TELEGRAM_BOT_NAME = "Project Teapot418"

    MODEL_NAME = "deepseek/deepseek-chat-v3-0324:free"
    MAX_CONTEXT_LENGTH = 10  # Number of message pairs to keep
    REDIS_URL = "redis://localhost:6379"
    RATE_LIMIT = 5  # Messages per minute per user
    REQUEST_TIMEOUT = 30  # seconds
    MAX_RETRIES = 3
    LOGS_DIR = "logs"

    # Localization settings
    DEFAULT_LANGUAGE = "ru"  # "en" for English, "ru" for Russian

config = Config()
