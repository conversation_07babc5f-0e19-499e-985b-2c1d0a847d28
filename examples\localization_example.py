"""
Example of how to use the localization system with python-i18n.
This demonstrates how to add new text, change languages, and extend the system.
"""

import sys
import os

# Add the parent directory to the path so we can import from models
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.localization import localization, Language
from models.i18n_config import get_text, set_locale, get_locale


def main():
    print("=== Localization System Example (python-i18n) ===\n")

    # Show current default language
    print(f"Current default language: {localization.default_language.value}")
    print(f"Current i18n locale: {get_locale()}")

    # Get some existing text in default language (Russian)
    print(f"Rate limit message (RU): {localization.get_text('rate_limit_exceeded')}")

    # Get the same text in English
    print(f"Rate limit message (EN): {localization.get_text('rate_limit_exceeded', Language.ENGLISH)}")

    print("\n" + "="*50 + "\n")

    # Demonstrate direct python-i18n usage
    print("Direct python-i18n usage:")
    print(f"Direct i18n call (current locale): {get_text('rate_limit_exceeded')}")

    # Temporarily switch locale
    set_locale("en")
    print(f"After setting locale to 'en': {get_text('rate_limit_exceeded')}")
    set_locale("ru")  # Switch back

    print("\n" + "="*50 + "\n")

    # Add new custom text
    localization.add_text("welcome_back", {
        "en": "Welcome back! How can I help you today with %{task}?",
        "ru": "С возвращением! Как я могу помочь вам сегодня с %{task}?"
    })

    print("Added new text 'welcome_back':")
    print(f"English: {localization.get_text('welcome_back', Language.ENGLISH, task='coding')}")
    print(f"Russian: {localization.get_text('welcome_back', Language.RUSSIAN, task='программированием')}")

    print("\n" + "="*50 + "\n")

    # Change default language to English
    localization.set_default_language(Language.ENGLISH)
    print(f"Changed default language to: {localization.default_language.value}")
    print(f"Current i18n locale: {get_locale()}")

    # Now get text without specifying language (should be English)
    print(f"Rate limit message (default): {localization.get_text('rate_limit_exceeded')}")
    print(f"Welcome back (default): {localization.get_text('welcome_back', task='testing')}")

    print("\n" + "="*50 + "\n")

    # Show all available text keys
    print("All available text keys:")
    for key in localization.get_all_keys():
        print(f"  - {key}")

    print("\n" + "="*50 + "\n")

    # Demonstrate text formatting with python-i18n placeholders
    print("Text formatting example (note: uses %{placeholder} format):")
    start_text = localization.get_text("start_greeting", user="@TestUser")
    print(f"Start greeting: {start_text}")

    model_text = localization.get_text("current_model", model_name="gpt-4")
    print(f"Model info: {model_text}")

    print("\n" + "="*50 + "\n")

    # Demonstrate error handling
    print("Error handling examples:")
    missing_text = localization.get_text("nonexistent_key")
    print(f"Missing key: {missing_text}")

    # Test fallback to English
    set_locale("fr")  # Set to unsupported language
    fallback_text = get_text("rate_limit_exceeded")
    print(f"Fallback to English (fr->en): {fallback_text}")

    # Reset to Russian
    set_locale("ru")


if __name__ == "__main__":
    main()
