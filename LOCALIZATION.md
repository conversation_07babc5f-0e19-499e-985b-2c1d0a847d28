# Localization System

This document describes the localization system implemented for the Telegram bot using python-i18n.

## Overview

The localization system allows you to:
- Store all text messages in YAML files organized by language
- Support multiple languages (currently English and Russian)
- Configure a global default language
- Easily add new text or languages
- Format text with dynamic values using python-i18n placeholders
- Automatic fallback to English if translation is missing
- Memory caching for improved performance

## Configuration

### Setting Default Language

In `models/config.py`, set the `DEFAULT_LANGUAGE` variable:

```python
DEFAULT_LANGUAGE = "ru"  # For Russian (default)
DEFAULT_LANGUAGE = "en"  # For English
```

### Translation Files

Translation files are stored in the `locales/` directory as YAML files:
- `locales/en.yml` - English translations
- `locales/ru.yml` - Russian translations

Each file follows this structure:
```yaml
en:
  key_name: "Translation text with %{placeholder} support"
  another_key: |
    Multi-line text
    with proper formatting
```

## Usage

### Basic Usage

```python
from models.localization import localization, Language

# Get text in default language
message = localization.get_text("rate_limit_exceeded")

# Get text in specific language
message = localization.get_text("rate_limit_exceeded", Language.ENGLISH)

# Get text with formatting (note: uses %{placeholder} format)
greeting = localization.get_text("start_greeting", user="@username")
```

### Direct python-i18n Usage

You can also use the python-i18n library directly:

```python
from models.i18n_config import get_text, set_locale

# Set locale
set_locale("en")

# Get text
message = get_text("rate_limit_exceeded")

# Get text with parameters
greeting = get_text("start_greeting", user="@username")
```

### Available Text Keys

Current available text keys:
- `rate_limit_exceeded` - Rate limiting message
- `no_response` - Error when AI doesn't respond
- `unexpected_error` - General error message
- `start_greeting` - Welcome message (requires `user` parameter)
- `help_text` - Help command text
- `context_cleared` - Context reset success message
- `context_clear_failed` - Context reset failure message
- `current_model` - Model info (requires `model_name` parameter)

### Adding New Translations

To add new translations, edit the YAML files in the `locales/` directory:

```yaml
# In locales/en.yml
en:
  new_feature: "This is a new feature with %{parameter}!"

# In locales/ru.yml
ru:
  new_feature: "Это новая функция с %{parameter}!"
```

Then use the new text:

```python
# Use the new text
message = localization.get_text("new_feature", parameter="value")
```

### Adding Text Programmatically

You can also add translations programmatically:

```python
# Add new text with translations
localization.add_text("new_feature", {
    "en": "This is a new feature with %{parameter}!",
    "ru": "Это новая функция с %{parameter}!"
})

# Use the new text
message = localization.get_text("new_feature", parameter="value")
```

### Changing Language at Runtime

```python
# Change default language
localization.set_default_language(Language.ENGLISH)

# All subsequent calls will use English by default
message = localization.get_text("help_text")
```

## File Structure

```
locales/
├── en.yml              # English translations
└── ru.yml              # Russian translations

models/
├── localization.py     # Backward-compatible localization wrapper
├── i18n_config.py      # python-i18n configuration and setup
└── config.py          # Configuration including DEFAULT_LANGUAGE

examples/
└── localization_example.py  # Usage examples
```

## Adding New Languages

To add a new language:

1. Add the language to the `Language` enum in `models/i18n_config.py`:
```python
class Language(Enum):
    ENGLISH = "en"
    RUSSIAN = "ru"
    SPANISH = "es"  # New language
```

2. Create a new YAML file in the `locales/` directory (e.g., `locales/es.yml`):
```yaml
es:
  rate_limit_exceeded: "⚠️ Estás enviando mensajes muy rápido. Por favor espera un momento."
  no_response: "🚨 Lo siento, tengo problemas para procesar tu solicitud. Inténtalo de nuevo más tarde."
  # ... add all other translations
```

3. Update the config to use the new language:
```python
DEFAULT_LANGUAGE = "es"
```

## Telegram Markdown Formatting

The system handles Telegram's MarkdownV2 formatting automatically. Special characters are escaped with backslashes (`\`) where needed.

## Error Handling

The localization system includes fallback mechanisms:
- If a text key doesn't exist, returns `[Missing text: key]`
- If a language isn't available, falls back to English
- If English isn't available, returns `[Missing translation: key]`
- If formatting fails, returns `[Format error in key: missing parameter]`

## Migration from Old System

The old hardcoded strings have been replaced with localization calls:

**Before:**
```python
too_fast_ru = "⚠️ Вы отправляете сообщения слишком быстро. Пожалуйста, подождите немного."
await update.message.reply_text(too_fast_ru)
```

**After:**
```python
await update.message.reply_text(localization.get_text("rate_limit_exceeded"))
```

## Example Script

Run the example script to see the localization system in action:

```bash
python examples/localization_example.py
```

This will demonstrate:
- Getting text in different languages
- Adding new text
- Changing the default language
- Text formatting with parameters
