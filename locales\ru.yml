ru:
  # Rate limiting messages
  rate_limit_exceeded: "⚠️ Вы отправляете сообщения слишком быстро. Пожалуйста, подождите немного."
  
  # Error messages
  no_response: "🚨 Извините, у меня возникли проблемы с обработкой вашего запроса. Пожалуйста, попробуйте позже."
  unexpected_error: "⚠️ Произошла непредвиденная ошибка. Пожалуйста, попробуйте еще раз позже."
  
  # Start command
  start_greeting: |
    👋 Привет, %{user}\! Я ИИ\-ассистент на платформе OpenRouter\.

    Просто отправь мне сообщение и я отвечу\. Я запоминаю контекст нашего разговора\! Используй /reset, чтобы очистить историю переписки\.
  
  # Help command
  help_text: |
    🤖 *Помощь по боту*

    \- Просто отправьте мне сообщение \- и я отвечу
    \- Я запоминаю контекст нашего разговора
    \- Используйте /reset чтобы очистить историю беседы
    \- Используйте /model чтобы узнать текущую модель ИИ

    ⚠️ Пожалуйста, проявляйте терпение, если я отвечаю медленно \- я получаю много сообщений\!
  
  # Reset context
  context_cleared: "🔄 История переписки очищена. Давайте начнем с чистого листа!"
  context_clear_failed: "⚠️ Не удалось очистить историю. Пожалуйста, попробуйте еще раз."
  
  # Model info
  current_model: "🤖 В настоящее время используется: %{model_name}"
